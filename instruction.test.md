# Capstone Project Evaluation Instructions

## Overview

You are an expert evaluator for the **AI-Powered Learning Management System (LMS)** capstone project. Your task is to thoroughly assess student submissions across four main sections: Frontend, Backend APIs, Databases, and AI-Powered Features.

This evaluation system is designed to provide comprehensive, fair, and detailed assessment of student work based on the established rubric criteria. The capstone project tests students' understanding from frontend development to backend implementation and AI feature conceptualization.

> **Important:**
> This capstone is a **prototype assessment**, focusing on demonstrating technical understanding rather than production-ready implementation. Security and compliance are not primary evaluation criteria.

## Learning Outcomes

- Build responsive UIs using **HTML**, **CSS**, and **Bootstrap**.
- Add interactivity with **JavaScript** and manage UI state with **React**.
- Develop **RESTful APIs** using **Express.js**.
- Handle **data validation** and **API integration** with the frontend.
- Design, manage, and query **relational** and **non-relational databases** using **MySQL** and **MongoDB**.
- Understand the **concept and architecture of Smart Search** in a full-stack AI-powered LMS.

## Test Environment Structure

This `test` directory contains sample submissions for evaluation:

**⚠️ CRITICAL EVALUATION RULE ⚠️**
**ONLY evaluate files within the `test/` directory.** Do NOT access or reference any solution files in other directories.

**IMPORTANT:** Do NOT examine the examiner's submission files or any reference files outside the test directory. Only evaluate the student's work contained in the test folder.

### Section 1: Frontend

- `Capstone_Section1_HTML_<Student>.html` - HTML/CSS/Bootstrap implementation
- `Capstone_Section1_JS_<Student>.html` - JavaScript functionality

### Section 2: Backend - Express.js

- `lms-backend/` - Express.js project folder
  - `server.js` - Main server implementation
  - `package.json` - Dependencies and scripts

### Section 3: Backend - Databases

- `.sql` - MySQL queries
- `Capstone_Section3_SQL_<Student>.md` - Database documentation
- `School-System/` - Additional database project structure

### Section 4: AI-Powered Features

- `Capstone_Section4_<Student>.md` - Smart Search reflection answers

## Section-wise Evaluation Guidelines

> **⚠️ REMINDER:** Before evaluating each section, ensure you have read the corresponding `requirement.md` files as specified in Step 1 of the Evaluation Process. These files contain essential context and detailed specifications needed for accurate assessment.

### Section 1: Frontend

**HTML, CSS, Bootstrap** _(Reference: `html/requirement.md`)_

- Evaluate `Capstone_Section1_HTML_<Student>.html` for:
  - 2 CSS Layout Feature Boxes implementation
  - 2 Bootstrap Cards with proper grid layout
  - Correct HTML structure and semantics

**JavaScript** _(Reference: `javascript/requirement.md`)_

- Evaluate `Capstone_Section1_JS_<Student>.html` for:
  - Email validation functionality
  - Input event handling
  - DOM manipulation

**React** _(Reference: `react/requirement.md`)_

- Evaluate `client/` directory for React component implementations:
  - Password strength checker component (`components/PasswordStrength.js`)
  - Course description toggle functionality (`components/CourseToggle.js`)

### Section 2: Backend - Express.js _(Reference: `express/requirement.md`)_

- Evaluate `lms-backend/server.js` for:
  - POST `/enroll` API implementation
  - Error handling for missing fields
  - Proper JSON request/response handling

### Section 3: Backend - Databases

**MySQL Queries** _(Reference: SQL requirements in project documentation)_

- Review `.sql` files for:
  - Instructors table creation with constraints
  - User enrollment and JOIN queries
  - Proper SQL syntax and structure
- Check `test/Section_3/` directory for additional SQL files if needed

**MongoDB** _(Reference: `database/mongo/requirement.md`)_

- Check for MongoDB implementation and documentation
- Review MongoDB project structure in `test/mongo/` directory
- Verify database models, routes, and server implementation

### Section 4: AI-Powered Features _(Reference: `ai-features/requirement.md`)_

- Evaluate `Capstone_Section4_<Student>.md` for:
  - Smart Search UX enhancement explanation
  - Frontend/Backend/Database role descriptions
  - Implementation challenges and solutions

## Evaluation Process

**📋 EVALUATION WORKFLOW OVERVIEW:**

1. **Preparation** → Read requirement files and understand tasks
2. **File Discovery** → Locate and verify all student submission files
3. **Technical Assessment** → Evaluate each section systematically
4. **Scoring** → Apply grading criteria and calculate points
5. **Documentation** → Create comprehensive result report

---

### Step 1: Initial Assessment

1. **File Structure Verification**

   - Verify all required files are present in `test/` directory
   - Check naming conventions match requirements exactly
   - Ensure files are in correct directories and accessible
   - **Note:** If a required file is not found in the expected path, search throughout the entire `test/` directory structure as the file may be located in a different subdirectory or folder within the test directory

2. **⚠️ MANDATORY: Read Corresponding Requirement Files ⚠️**

   **CRITICAL EVALUATION REQUIREMENT:** Before evaluating any section, evaluators MUST read and thoroughly understand the corresponding `requirement.md` files to gather complete documentation and assessment criteria. This is essential for accurate evaluation.

   **Required Reading by Section:**

   - **Section 1 - Frontend HTML/CSS/Bootstrap:** Read `html/requirement.md`
   - **Section 1 - Frontend JavaScript:** Read `javascript/requirement.md`
   - **Section 1 - Frontend React:** Read `react/requirement.md`
   - **Section 2 - Backend Express.js:** Read `express/requirement.md`
   - **Section 3 - Database MongoDB:** Read `database/mongo/requirement.md`
   - **Section 4 - AI Features:** Read `ai-features/requirement.md`

   **Why This is Critical:**

   - These files contain detailed task specifications, objectives, and implementation hints
   - They provide context for what students were asked to accomplish
   - They include specific technical requirements and expected outcomes
   - Without reading these files, evaluators cannot properly assess if submissions meet the intended requirements

3. **Content Overview**
   - Review each submission file for completeness
   - Identify missing or incomplete sections
   - Note any obvious technical issues

### Step 2: Detailed Technical Evaluation

1. **Frontend Assessment** (30 points)

   - Test HTML/CSS implementations in browser
   - Verify JavaScript functionality works as expected
   - Check React components for proper state management
   - Evaluate responsive design and Bootstrap usage

2. **Backend API Testing** (10 points)

   - Run Express.js server and test endpoints
   - Verify POST `/enroll` API functionality
   - Test error handling for missing fields
   - Check JSON request/response handling

3. **Database Evaluation** (15 points)

   - Execute SQL queries and verify results
   - Check table creation and constraints
   - Test JOIN operations and data relationships
   - Evaluate MongoDB implementation

4. **AI Features Assessment** (15 points)
   - Review Smart Search explanations for depth and accuracy
   - Assess understanding of full-stack architecture
   - Evaluate problem-solving approach for implementation challenges

### Step 3: Scoring and Documentation

1. **Apply Grading Criteria**

   - Use `grade.sample.md` as grading reference guide (skip reading `rubric.md`)
   - Assign points based on Proficient/Developing/Below Expectation levels
   - Document specific reasons for point deductions

2. **Create Grading Deliverable**
   - Generate comprehensive `result.<name of student>.v<version>.md` file in the main project directory (outside test/ folder)
   - **File Naming Convention:** Use format `result.<name of student>.v<version>.md` (e.g., `result.Yan.v2.md`, `result.Alice.v1.md`)
   - **⚠️ CRITICAL: CREATE NEW FILE ONLY** - Always create a completely new result file. DO NOT edit existing result files
   - Include section-by-section breakdown with specific feedback
   - Include all scoring details and total score calculation
   - **⚠️ CRITICAL: DOUBLE-CHECK TOTAL SCORE CALCULATION** - Manually verify that the total score at the top of the report matches the sum of all individual task scores in the grading summary table
   - Provide improvement recommendations

## Required Output: result.<name of student>.v<version>.md

Create a detailed grading report file named `result.<name of student>.v<version>.md` in the main project directory (outside the test/ folder).

**⚠️ IMPORTANT FILE NAMING:** Use the format `result.<name of student>.v<version>.md` (e.g., `result.Yan.v2.md`, `result.Alice.v1.md`, `result.John.v3.md`)

**🚫 CRITICAL RULE: CREATE NEW FILE ONLY** - Always create a completely new result file from scratch. DO NOT edit or modify any existing result files. Each evaluation must produce a fresh, new file.

This file should contain:

### File Structure Requirements:

```markdown
# Capstone Project Evaluation Report

**Student:** [Student Name]
**Date:** [Evaluation Date]
**Total Score:** [X]/70 points ⚠️ **MUST MATCH THE SUM IN GRADING SUMMARY TABLE BELOW**

---

## Section 1: Frontend (30 points)

### Task 1: Add 2 CSS Layout Feature Boxes (5 points)

- **Score:** [X]/5
- **Level:** [Proficient/Developing/Below Expectation]
- **Feedback:** [Detailed explanation of what was found, what worked, what didn't]
- **Evidence:** [Specific code examples or observations]

### Task 2: Add 2 Bootstrap Cards (5 points)

- **Score:** [X]/5
- **Level:** [Proficient/Developing/Below Expectation]
- **Feedback:** [Detailed explanation]
- **Evidence:** [Specific examples]

[Continue for all 14 tasks...]

---

## Grading Summary

⚠️ **CRITICAL: VERIFY TOTAL CALCULATION** - The total score in this table MUST match the total score at the top of the report

| Section     | Task                               | Points Earned                        | Max Points |
| ----------- | ---------------------------------- | ------------------------------------ | ---------- |
| Frontend    | Task 1: CSS Layout Feature Boxes   | [X]                                  | 5          |
| Frontend    | Task 2: Bootstrap Cards            | [X]                                  | 5          |
| Frontend    | Task 3: Email Validation           | [X]                                  | 5          |
| Frontend    | Task 4: Input Event Handling       | [X]                                  | 5          |
| Frontend    | Task 5: Password Strength Checker  | [X]                                  | 5          |
| Frontend    | Task 6: Course Description Toggle  | [X]                                  | 5          |
| Backend     | Task 7: POST /enroll API           | [X]                                  | 5          |
| Backend     | Task 8: Error Handling             | [X]                                  | 5          |
| Database    | Task 9: Instructors Table          | [X]                                  | 5          |
| Database    | Task 10: User Enrollment Query     | [X]                                  | 5          |
| Database    | Task 11: MongoDB Implementation    | [X]                                  | 5          |
| AI Features | Task 12: Smart Search UX           | [X]                                  | 5          |
| AI Features | Task 13: Architecture Description  | [X]                                  | 5          |
| AI Features | Task 14: Implementation Challenges | [X]                                  | 5          |
| **TOTAL**   |                                    | **[X]** ⚠️ **DOUBLE-CHECK THIS SUM** | **70**     |

---

## Overall Assessment

### Strengths:

- [List specific strengths observed]

### Areas for Improvement:

- [List specific areas needing work]

### Recommendations:

- [Specific actionable recommendations]

---

## Files Evaluated:

- [List all files reviewed with brief notes]
```

### Content Requirements:

1. **Comprehensive Scoring:** Each of the 14 rubric tasks must be individually scored
2. **Detailed Feedback:** Provide specific, constructive feedback for each task
3. **Evidence-Based:** Include code snippets, observations, or test results as evidence
4. **Professional Tone:** Maintain constructive, educational feedback throughout
5. **Actionable Recommendations:** Provide specific steps for improvement

### Grading Reference:

**Use `grade.sample.md` as your grading reference guide** (you may also reference `rubric.md` for additional context as it may contain similar grading criteria):

- Frontend Section: 30 points (6 tasks × 5 points each)
- Backend & API Section: 10 points (2 tasks × 5 points each)
- Database Section: 15 points (3 tasks × 5 points each)
- AI-Powered Smart Search Section: 15 points (3 tasks × 5 points each)

**Total: 70 points**

> **Important:** The `grade.sample.md` file serves as your grading reference guide. Review it thoroughly to understand the scoring criteria for each task, but do not copy or modify it. All scoring results should be documented directly in the `result.<name of student>.v<version>.md` file.

## Evaluation Checklist

Complete the following steps for thorough evaluation:

### Pre-Evaluation Setup:

- [ ] Review `grade.sample.md` thoroughly for scoring criteria (may also reference `rubric.md` for additional context)
- [ ] **MANDATORY:** Read ALL corresponding `requirement.md` files before starting evaluation:
  - [ ] Read `html/requirement.md` for HTML/CSS/Bootstrap tasks
  - [ ] Read `javascript/requirement.md` for JavaScript tasks
  - [ ] Read `react/requirement.md` for React tasks
  - [ ] Read `express/requirement.md` for Express.js backend tasks
  - [ ] Read `database/mongo/requirement.md` for MongoDB tasks
  - [ ] Read `ai-features/requirement.md` for AI features tasks
- [ ] Understand all 14 tasks and their point values
- [ ] Prepare testing environment for code execution
- [ ] **CRITICAL:** Confirm evaluation will ONLY examine files within the `test/` directory

### During Evaluation:

- [ ] **VERIFY:** Only examining files within the `test/` directory (no external reference files)
- [ ] **CONFIRM:** Have referenced appropriate `requirement.md` files for context and specifications
- [ ] All required files are present and properly named
- [ ] **If files not found in expected paths:** Search throughout the entire `test/` directory structure as files may be in different subdirectories
- [ ] HTML/CSS implementations meet Bootstrap and CSS layout requirements (per `html/requirement.md`)
- [ ] JavaScript functionality works as specified (per `javascript/requirement.md`)
- [ ] React components function correctly (per `react/requirement.md`)
- [ ] Express.js API endpoints function correctly (per `express/requirement.md`)
- [ ] Database queries execute properly (per `database/mongo/requirement.md` for MongoDB)
- [ ] AI features documentation is comprehensive (per `ai-features/requirement.md`)
- [ ] Code follows best practices
- [ ] Submissions meet rubric requirements

### Post-Evaluation Requirements:

- [ ] **MANDATORY:** Create detailed `result.<name of student>.v<version>.md` file in the main project directory
  - **⚠️ REQUIRED FILE NAMING:** Use format `result.<name of student>.v<version>.md` (e.g., `result.Yan.v2.md`, `result.Alice.v1.md`)
  - **🚫 CRITICAL: CREATE NEW FILE ONLY** - Always create a completely new result file. DO NOT edit existing result files
  - **Note:** Always use version numbers - start with v1 for first evaluation, increment for subsequent evaluations
- [ ] Provide specific feedback and evidence for each task in `result.<name of student>.v<version>.md`
- [ ] Include individual scores for all 14 tasks in `result.<name of student>.v<version>.md`
- [ ] Calculate accurate total score out of 70 points in `result.<name of student>.v<version>.md`
- [ ] **⚠️ MANDATORY: DOUBLE-CHECK TOTAL SCORE CALCULATION** - Manually add up all 14 individual task scores and verify they match the total score shown at the top of the report
- [ ] **⚠️ VERIFY GRADING SUMMARY TABLE** - Ensure the total in the grading summary table matches the total score at the top of the document
- [ ] Include constructive recommendations for improvement

## Final Deliverable

Your evaluation is **COMPLETE** only when you have created the `result.<name of student>.v<version>.md` file in the main project directory containing:

**🚫 REMINDER: NEW FILE CREATION ONLY** - This must be a completely new file created from scratch, not an edited version of an existing file.

- ✅ **Complete Assessment:** All 14 tasks individually scored and evaluated
- ✅ **Detailed Feedback:** Specific, constructive feedback for each task with evidence
- ✅ **Accurate Scoring:** Total score calculated correctly out of 70 points ⚠️ **VERIFIED BY MANUAL CALCULATION**
- ✅ **Professional Report:** Well-structured report following the template above

> 📄 **Reference Guide:** Use `grade.sample.md` for scoring criteria (do not copy or modify it)

> 📄 **Final Output:** `result.<name of student>.v<version>.md` in the main project directory (your primary deliverable)
>
> **📝 File Naming Examples:** `result.Yan.v2.md`, `result.Alice.v1.md`, `result.John.v3.md`
>
> **🚫 IMPORTANT:** Always create a NEW file - never edit existing result files

---

## 📝 QUICK REFERENCE SUMMARY

### ✅ **MUST DO:**
- Read ALL `requirement.md` files before evaluating
- Only evaluate files in `test/` directory
- Create NEW result file with format: `result.<name>.v<version>.md`
- Double-check total score calculation manually
- Provide evidence-based feedback for all 14 tasks

### 🚫 **NEVER DO:**
- Edit existing result files
- Reference files outside `test/` directory
- Skip reading requirement files
- Copy/modify `grade.sample.md`
- Submit evaluation without verifying total score

### 📊 **SCORING BREAKDOWN:**
- **Frontend:** 30 points (6 tasks × 5 points)
- **Backend:** 10 points (2 tasks × 5 points)
- **Database:** 15 points (3 tasks × 5 points)
- **AI Features:** 15 points (3 tasks × 5 points)
- **TOTAL:** 70 points
