# Capstone Project Evaluation Report

**Student:** Yan
**Date:** [2024-06-29]
**Total Score:** 65/70 points

---

## Section 1: Frontend (30 points)

### Task 1: Add 2 CSS Layout Feature Boxes (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** All three feature boxes ("Adaptive Courses", "Progress Tracking", "Real-time Assessments") are present in a flexbox layout. Titles and structure match requirements.
- **Evidence:**
  ```html
  <section class="feature-box">
    <div class="card-flex">
      <h4>Adaptive Courses</h4>
      ...
    </div>
    <div class="card-flex">
      <h4>Progress Tracking</h4>
      ...
    </div>
    <div class="card-flex">
      <h4>Real-time Assessments</h4>
      ...
    </div>
  </section>
  ```

### Task 2: Add 2 Bootstrap Cards (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Two Bootstrap cards (HTML Module, CSS Module) are implemented side by side using Bootstrap grid and card classes. Each card includes title, text, and button.
- **Evidence:**
  ```html
  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">HTML Module</h5>
          ...
        </div>
      </div>
    </div>
    <div class="col-md-6 mb-4">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">CSS Module</h5>
          ...
        </div>
      </div>
    </div>
  </div>
  ```

### Task 3: Email Validation (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The email validation function checks for the presence of "@" and updates the DOM with appropriate messages. Prevents form submission if invalid.
- **Evidence:**
  ```js
  function validateEmail(event) {
    event.preventDefault();
    let emailInput = document.getElementById("email").value;
    var emailMessage = document.getElementById("emailMessage");
    if (!emailInput.includes("@")) {
      emailMessage.textContent = "Invalid email address";
      return false;
    }
    emailMessage.textContent = "Email accepted!";
  }
  ```

### Task 4: Input Event Handling (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The input event handler updates the output paragraph dynamically as the user types their goal, as required.
- **Evidence:**
  ```js
  goalInput.addEventListener("input", function () {
    var text = goalInput.value;
    document.getElementById("goalOutput").textContent = "Your goal: " + text;
  });
  ```

### Task 5: Password Strength Checker (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The PasswordStrength React component checks password length and presence of a number, displaying appropriate messages. Uses useState and conditional logic as required.
- **Evidence:**
  ```js
  if (password.length < 6) {
    setMessage("Weak password");
  } else if (/\d/.test(password)) {
    setMessage("Strong password");
  } else {
    setMessage("Password should include a number");
  }
  ```

### Task 6: Course Description Toggle (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The CourseToggle React component toggles the course description with a button, updating the label and using conditional rendering.
- **Evidence:**
  ```js
  <button onClick={toggleDescription}>
    {isVisible ? "Hide Description" : "Show Description"}
  </button>;
  {
    isVisible && <p>...</p>;
  }
  ```

---

## Section 2: Backend & API (10 points)

### Task 7: POST /enroll API (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The /enroll endpoint accepts JSON, checks for userId and courseId, and returns a confirmation message as required.
- **Evidence:**
  ```js
  app.post('/enroll', (req, res) => {
    const { userId, courseId } = req.body;
    ...
    res.json({ message: `User ${userId} successfully enrolled in course ${courseId}.` });
  });
  ```

### Task 8: Error Handling (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Returns a 400 error and proper message when userId or courseId is missing from the request body.
- **Evidence:**
  ```js
  if (!userId || !courseId) {
    return res
      .status(400)
      .json({ error: "Missing userId or courseId in request." });
  }
  ```

---

## Section 3: Database (15 points)

### Task 9: Instructors Table (5 points)

- **Score:** 4/5
- **Level:** Developing
- **Feedback:** The instructors table is created with AUTO_INCREMENT and UNIQUE constraints. However, only two instructor records are inserted instead of the required three.
- **Evidence:**
  ```sql
  CREATE TABLE instructors (
    instructor_id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50),
    email VARCHAR(50) UNIQUE
  );
  INSERT INTO instructors(name, email)
  VALUES
  ('Brown', '<EMAIL>'),
  ('Yellow', '<EMAIL>');
  ```

### Task 10: User Enrollment Query (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The SQL includes user creation, enrollment, and a JOIN query to display all users enrolled in "CSS Design". Syntax and logic are correct.
- **Evidence:**
  ```sql
  INSERT INTO users(name, email, password)
  VALUES('Daniel', '<EMAIL>', 'daniel123');
  INSERT INTO enrollments(user_id, course_id, enrollment_date)
  VALUES(4, 2, CURRENT_DATE());
  SELECT u.name, u.email
  FROM users u JOIN enrollments e ON u.user_id = e.user_id
  JOIN courses c ON e.course_id = c.course_id
  WHERE c.course_name = 'CSS Design';
  ```

### Task 11: MongoDB Implementation (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** MongoDB documents for schools, courses, and enrollments are provided with correct fields and structure. Mongoose models and routes are implemented for all entities.
- **Evidence:**
  - `schoolModel.js`, `courseModel.js`, `enrollmentModel.js` schemas
  - Example document:
    ```js
    {
      _id: { $oid: "665f1fa4a7d3f1a0aabc1001" },
      name: "Greenwood High School",
      address: "123 Maple Street, Springfield",
      principal: "Mr. John Adams"
    }
    ```

---

## Section 4: AI Features (15 points)

### Task 12: Smart Search UX (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The explanation clearly describes how Smart Search enhances the LMS experience, including intent recognition, synonyms, and spelling correction.
- **Evidence:**
  > "Smart Search enhances the learning experience in an LMS by helping users find relevant content without needing exact keywords..."

### Task 13: Architecture Description (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** The roles of frontend, backend, and database are clearly explained, with accurate description of their interactions in Smart Search.
- **Evidence:**
  > "Technically, the frontend (using React or JavaScript) captures user input in real time and sends it to the backend..."

### Task 14: Implementation Challenges (5 points)

- **Score:** 5/5
- **Level:** Proficient
- **Feedback:** Identifies key challenges (performance, intent interpretation) and suggests conceptual solutions (NLP, synonym mapping).
- **Evidence:**
  > "A key challenge is optimizing search performance and accurately interpreting user intent. This can be addressed by integrating simple NLP techniques..."

---

## Grading Summary

| Section     | Task                               | Points Earned | Max Points |
| ----------- | ---------------------------------- | ------------- | ---------- |
| Frontend    | Task 1: CSS Layout Feature Boxes   | 5             | 5          |
| Frontend    | Task 2: Bootstrap Cards            | 5             | 5          |
| Frontend    | Task 3: Email Validation           | 5             | 5          |
| Frontend    | Task 4: Input Event Handling       | 5             | 5          |
| Frontend    | Task 5: Password Strength Checker  | 5             | 5          |
| Frontend    | Task 6: Course Description Toggle  | 5             | 5          |
| Backend     | Task 7: POST /enroll API           | 5             | 5          |
| Backend     | Task 8: Error Handling             | 5             | 5          |
| Database    | Task 9: Instructors Table          | 4             | 5          |
| Database    | Task 10: User Enrollment Query     | 5             | 5          |
| Database    | Task 11: MongoDB Implementation    | 5             | 5          |
| AI Features | Task 12: Smart Search UX           | 5             | 5          |
| AI Features | Task 13: Architecture Description  | 5             | 5          |
| AI Features | Task 14: Implementation Challenges | 5             | 5          |
| **TOTAL**   |                                    | **65**        | **70**     |

---

## Overall Assessment

### Strengths:

- Excellent use of modern HTML, CSS, and Bootstrap for layout and design
- JavaScript and React components are well-structured and functional
- Backend API and error handling are robust and follow best practices
- Database schemas and queries are clear and correct
- AI features section demonstrates strong conceptual understanding

### Areas for Improvement:

- Ensure all requirements are fully met (e.g., insert three instructor records as specified)
- Add more test coverage for React components
- Provide more detailed comments in code for maintainability

### Recommendations:

- Double-check all rubric requirements for completeness
- Expand on database examples and add more sample data
- Consider adding automated tests for frontend and backend components
- Continue practicing clear, maintainable code documentation

---

## Files Evaluated:

- test/Section1/Capstone_Section1_HTML_Yan.html (HTML/CSS/Bootstrap)
- test/Section1/Capstone_Section1_JS_Yan.html (JavaScript)
- test/Section1/Capstone_Section1_React_Yan/src/components/PasswordStrength.js (React Password Strength)
- test/Section1/Capstone_Section1_React_Yan/src/components/CourseToggle.js (React Course Toggle)
- test/Section1/Capstone_Section1_React_Yan/src/App.js (React App usage)
- test/Section2/Capstone_Section2_Yan/server.js (Express.js backend)
- test/Section3/Capstone_Section3_SQL_Yan.md (MySQL queries)
- test/Section3/Capstone_Section3_MongoDB_Yan.md (MongoDB documentation)
- test/Section3/Back_end/server.js (MongoDB/Express backend)
- test/Section3/Back_end/models/schoolModel.js (Mongoose schema)
- test/Section3/Back_end/models/courseModel.js (Mongoose schema)
- test/Section3/Back_end/models/enrollmentModel.js (Mongoose schema)
- test/Section3/Back_end/routes/schoolRoutes.js (Express route)
- test/Section3/Back_end/routes/courseRoutes.js (Express route)
- test/Section3/Back_end/routes/enrollmentRoutes.js (Express route)
- test/Section4/Capstone_Section4_Yan.md (AI features reflection)
